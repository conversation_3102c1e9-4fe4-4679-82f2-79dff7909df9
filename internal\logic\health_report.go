package logic

import (
	"context"
	"fmt"
	"math"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/service"
)

// healthReportLogic 健康报告业务逻辑实现
type healthReportLogic struct {
	nutritionStatService       service.INutritionStatService
	nutritionAdviceService     service.INutritionAdviceService
	userService               service.IUserService
	userNutritionGoalService  service.IUserNutritionGoalService
}

// NewHealthReportLogic 创建健康报告业务逻辑实例
func NewHealthReportLogic(
	nutritionStatService service.INutritionStatService,
	nutritionAdviceService service.INutritionAdviceService,
	userService service.IUserService,
	userNutritionGoalService service.IUserNutritionGoalService,
) service.IHealthReportService {
	return &healthReportLogic{
		nutritionStatService:      nutritionStatService,
		nutritionAdviceService:    nutritionAdviceService,
		userService:              userService,
		userNutritionGoalService: userNutritionGoalService,
	}
}

// 确保 healthReportLogic 实现了 IHealthReportService 接口
var _ service.IHealthReportService = &healthReportLogic{}

// GetHealthReport 获取用户健康报告
func (h *healthReportLogic) GetHealthReport(ctx context.Context, userID int64, date string) (*v1.HealthReportResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 解析日期
	parsedDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, &ParameterError{Field: "date", Message: "invalid date format, should be yyyy-MM-dd"}
	}

	// 获取用户营养目标
	nutritionGoal, err := h.userNutritionGoalService.GetNutritionGoal(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get nutrition goal: %w", err)
	}

	// 获取当前日期的营养统计数据
	currentNutritionStat, err := h.nutritionStatService.GetDailyNutritionStat(ctx, userID, date)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get current nutrition stat: %w", err)
	}

	// 计算上周同一天的日期
	lastWeekDate := parsedDate.AddDate(0, 0, -7)
	lastWeekDateStr := lastWeekDate.Format("2006-01-02")

	// 获取上周同一天的营养统计数据
	lastWeekNutritionStat, err := h.nutritionStatService.GetDailyNutritionStat(ctx, userID, lastWeekDateStr)
	if err != nil {
		// 如果获取上周数据失败，创建一个空的统计数据
		lastWeekNutritionStat = &v1.NutritionStatResponse{
			Date:              lastWeekDateStr,
			Calorie:           0,
			Protein:           0.0,
			Carbs:             0.0,
			Fat:               0.0,
			CaloriePercentage: 0.0,
			ProteinPercentage: 0.0,
			CarbsPercentage:   0.0,
			FatPercentage:     0.0,
		}
	}

	// 计算健康分数
	healthScore := h.CalculateHealthScore(ctx, currentNutritionStat, nutritionGoal)

	// 计算上周健康分数
	lastWeekHealthScore := h.CalculateHealthScore(ctx, lastWeekNutritionStat, nutritionGoal)

	// 计算分数变化
	scoreChange := healthScore - lastWeekHealthScore

	// 生成营养平衡数据
	nutritionBalance := h.CalculateNutritionBalance(ctx, currentNutritionStat, nutritionGoal)

	// 生成周进度对比数据
	weeklyProgress := h.CalculateWeeklyProgress(ctx, currentNutritionStat, lastWeekNutritionStat)

	// 获取营养建议
	advices, err := h.nutritionAdviceService.GetNutritionAdvice(ctx, userID, date)
	if err != nil {
		// 如果获取建议失败，使用空列表
		advices = []*v1.NutritionAdviceDisplayResponse{}
	}

	// 生成健康摘要建议
	suggestion := h.GenerateHealthSummary(ctx, healthScore, scoreChange)

	// 构建健康报告
	healthReport := &v1.HealthReportResponse{
		Date:             date,
		NutritionStat:    currentNutritionStat,
		Advices:          advices,
		WeeklyProgress:   weeklyProgress,
		Suggestion:       suggestion,
		HealthScore:      healthScore,
		ScoreChange:      scoreChange,
		NutritionBalance: nutritionBalance,
	}

	return healthReport, nil
}

// CalculateHealthScore 计算健康评分
func (h *healthReportLogic) CalculateHealthScore(ctx context.Context, nutritionStat *v1.NutritionStatResponse, nutritionGoal *v1.UserNutritionGoalResponse) int {
	if nutritionStat == nil {
		return 50 // 默认分数
	}

	// 计算卡路里得分（超标或不足都会扣分）
	var calorieScore float64
	if nutritionStat.CaloriePercentage > 100 {
		calorieScore = 100 - (nutritionStat.CaloriePercentage-100)*0.5
	} else {
		calorieScore = nutritionStat.CaloriePercentage
	}
	calorieScore = math.Max(0, calorieScore)

	// 计算蛋白质得分
	var proteinScore float64
	if nutritionStat.ProteinPercentage > 150 {
		proteinScore = 70
	} else {
		proteinScore = (nutritionStat.ProteinPercentage / 100) * 100
	}
	proteinScore = math.Max(0, proteinScore)

	// 计算碳水得分
	var carbsScore float64
	if nutritionStat.CarbsPercentage > 150 {
		carbsScore = 60
	} else {
		carbsScore = (nutritionStat.CarbsPercentage / 100) * 100
	}
	carbsScore = math.Max(0, carbsScore)

	// 计算脂肪得分
	var fatScore float64
	if nutritionStat.FatPercentage > 150 {
		fatScore = 50
	} else {
		fatScore = (nutritionStat.FatPercentage / 100) * 100
	}
	fatScore = math.Max(0, fatScore)

	// 总体健康分数（不同指标权重不同）
	totalScore := calorieScore*0.4 + proteinScore*0.3 + carbsScore*0.2 + fatScore*0.1

	return int(math.Min(100, math.Round(totalScore)))
}

// CalculateNutritionBalance 计算营养平衡数据
func (h *healthReportLogic) CalculateNutritionBalance(ctx context.Context, nutritionStat *v1.NutritionStatResponse, nutritionGoal *v1.UserNutritionGoalResponse) *v1.NutritionBalanceResponse {
	if nutritionStat == nil {
		return &v1.NutritionBalanceResponse{
			Protein: 0,
			Carbs:   0,
			Fat:     0,
		}
	}

	// 计算各项营养素达成百分比，最高150%
	proteinPercentage := int(math.Min(150, nutritionStat.ProteinPercentage))
	carbsPercentage := int(math.Min(150, nutritionStat.CarbsPercentage))
	fatPercentage := int(math.Min(150, nutritionStat.FatPercentage))

	return &v1.NutritionBalanceResponse{
		Protein: proteinPercentage,
		Carbs:   carbsPercentage,
		Fat:     fatPercentage,
	}
}

// CalculateWeeklyProgress 计算周进度对比数据
func (h *healthReportLogic) CalculateWeeklyProgress(ctx context.Context, currentStat *v1.NutritionStatResponse, lastWeekStat *v1.NutritionStatResponse) *v1.WeeklyProgressResponse {
	if currentStat == nil {
		currentStat = &v1.NutritionStatResponse{
			Calorie: 0,
			Protein: 0.0,
			Carbs:   0.0,
			Fat:     0.0,
		}
	}

	if lastWeekStat == nil {
		lastWeekStat = &v1.NutritionStatResponse{
			Calorie: 0,
			Protein: 0.0,
			Carbs:   0.0,
			Fat:     0.0,
		}
	}

	// 创建热量对比
	calorieProgress := &v1.NutrientProgressResponse{
		LastWeek: float64(lastWeekStat.Calorie),
		ThisWeek: float64(currentStat.Calorie),
	}

	// 创建蛋白质对比
	proteinProgress := &v1.NutrientProgressResponse{
		LastWeek: lastWeekStat.Protein,
		ThisWeek: currentStat.Protein,
	}

	// 创建碳水对比
	carbsProgress := &v1.NutrientProgressResponse{
		LastWeek: lastWeekStat.Carbs,
		ThisWeek: currentStat.Carbs,
	}

	// 创建脂肪对比
	fatProgress := &v1.NutrientProgressResponse{
		LastWeek: lastWeekStat.Fat,
		ThisWeek: currentStat.Fat,
	}

	return &v1.WeeklyProgressResponse{
		Calorie: calorieProgress,
		Protein: proteinProgress,
		Carbs:   carbsProgress,
		Fat:     fatProgress,
	}
}

// GenerateHealthSummary 生成健康摘要建议
func (h *healthReportLogic) GenerateHealthSummary(ctx context.Context, healthScore int, scoreChange int) string {
	var suggestion string

	// 根据健康分数生成基础建议
	switch {
	case healthScore >= 90:
		suggestion = "您的营养摄入非常均衡，请继续保持良好的饮食习惯！"
	case healthScore >= 80:
		suggestion = "您的营养摄入基本合理，可以适当调整某些营养素的摄入比例。"
	case healthScore >= 70:
		suggestion = "您的营养摄入需要改善，建议关注营养均衡，增加蛋白质和维生素的摄入。"
	case healthScore >= 60:
		suggestion = "您的营养摄入存在较大问题，建议咨询营养师制定合理的饮食计划。"
	default:
		suggestion = "您的营养摄入严重不均衡，强烈建议寻求专业营养师的指导。"
	}

	// 根据分数变化添加趋势建议
	if scoreChange > 10 {
		suggestion += " 相比上周，您的营养状况有了显著改善，继续加油！"
	} else if scoreChange > 0 {
		suggestion += " 相比上周，您的营养状况有所改善。"
	} else if scoreChange == 0 {
		suggestion += " 相比上周，您的营养状况保持稳定。"
	} else if scoreChange > -10 {
		suggestion += " 相比上周，您的营养状况略有下降，需要注意调整。"
	} else {
		suggestion += " 相比上周，您的营养状况明显下降，请重视饮食健康。"
	}

	return suggestion
}
