package logic

import (
	"context"
	"fmt"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/service"
)

// dashboardLogic 仪表盘业务逻辑实现
type dashboardLogic struct {
	userService           service.IUserService
	dietRecordService     service.IDietRecordService
	dietRecordAdminService service.IDietRecordAdminService
	nutritionStatService  service.INutritionStatService
}

// NewDashboardLogic 创建仪表盘业务逻辑实例
func NewDashboardLogic(
	userService service.IUserService,
	dietRecordService service.IDietRecordService,
	dietRecordAdminService service.IDietRecordAdminService,
	nutritionStatService service.INutritionStatService,
) service.IDashboardService {
	return &dashboardLogic{
		userService:           userService,
		dietRecordService:     dietRecordService,
		dietRecordAdminService: dietRecordAdminService,
		nutritionStatService:  nutritionStatService,
	}
}

// 确保 dashboardLogic 实现了 IDashboardService 接口
var _ service.IDashboardService = &dashboardLogic{}

// GetDashboardStats 获取管理员仪表盘统计数据
func (d *dashboardLogic) GetDashboardStats(ctx context.Context, req *v1.DashboardStatsRequest) (*v1.DashboardStatsResponse, error) {
	// 确定查询日期
	var queryDate time.Time
	if req.Date != nil && *req.Date != "" {
		var err error
		queryDate, err = time.Parse("2006-01-02", *req.Date)
		if err != nil {
			return nil, &ParameterError{Field: "date", Message: "invalid date format, expected YYYY-MM-DD"}
		}
	} else {
		queryDate = time.Now()
	}

	// 获取总用户数
	totalUsers, err := d.userService.GetTotalUserCount(ctx)
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to get total user count: %w", err)
	}

	// 获取今日饮食记录数
	todayRecords, err := d.dietRecordService.CountDietRecordsByDate(ctx, queryDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to count today's diet records: %w", err)
	}

	// 获取营养达标率
	nutritionComplianceRate, err := d.nutritionStatService.CalculateNutritionComplianceRate(ctx, queryDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to calculate nutrition compliance rate: %w", err)
	}

	// 推荐准确率固定返回95%（与Java版本一致）
	recommendationAccuracy := 95

	// 构造响应
	response := &v1.DashboardStatsResponse{
		TotalUsers:              totalUsers,
		TodayRecords:            todayRecords,
		NutritionComplianceRate: nutritionComplianceRate,
		RecommendationAccuracy:  recommendationAccuracy,
		StatisticsDate:          queryDate.Format("2006-01-02"),
	}

	return response, nil
}

// GetNutritionTrend 获取用户营养摄入趋势数据
func (d *dashboardLogic) GetNutritionTrend(ctx context.Context, req *v1.NutritionTrendRequest) (*v1.NutritionTrendResponse, error) {
	// 参数验证
	if req.Period == "" {
		return nil, &ParameterError{Field: "period", Message: "is required"}
	}

	// 调用营养统计服务获取全体用户营养趋势
	trendData, err := d.nutritionStatService.GetAllNutritionTrend(ctx, req.Period)
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to get nutrition trend: %w", err)
	}

	// 转换为响应格式
	response := &v1.NutritionTrendResponse{
		Period: req.Period,
	}

	// 安全地提取数据
	if dateList, ok := trendData["dateList"].([]string); ok {
		response.DateList = dateList
		response.DataPoints = len(dateList)
	}

	if calorieList, ok := trendData["calorieList"].([]float64); ok {
		response.CalorieList = calorieList
	}

	if proteinList, ok := trendData["proteinList"].([]float64); ok {
		response.ProteinList = proteinList
	}

	if carbsList, ok := trendData["carbsList"].([]float64); ok {
		response.CarbsList = carbsList
	}

	if fatList, ok := trendData["fatList"].([]float64); ok {
		response.FatList = fatList
	}

	return response, nil
}

// GetLatestDietRecords 获取最新饮食记录列表
func (d *dashboardLogic) GetLatestDietRecords(ctx context.Context, req *v1.DietRecordQueryRequest) (*v1.DietRecordListResponse, error) {
	// 参数验证
	if req.Page <= 0 {
		return nil, &ParameterError{Field: "page", Message: "must be positive"}
	}
	if req.Size <= 0 {
		return nil, &ParameterError{Field: "size", Message: "must be positive"}
	}

	// 转换为管理端查询请求
	adminReq := &v1.DietRecordQueryReq{
		Current: req.Page,
		Size:    req.Size,
	}

	if req.UserID != nil {
		adminReq.UserID = req.UserID
	}
	if req.StartDate != nil {
		adminReq.StartDate = req.StartDate
	}
	if req.EndDate != nil {
		adminReq.EndDate = req.EndDate
	}
	if req.MealType != nil {
		adminReq.MealType = req.MealType
	}

	// 调用管理端饮食记录服务
	response, err := d.dietRecordAdminService.ListDietRecords(ctx, adminReq)
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to get latest diet records: %w", err)
	}

	return response, nil
}

// GetDietRecordDetail 获取饮食记录详情
func (d *dashboardLogic) GetDietRecordDetail(ctx context.Context, recordID int64) (*v1.DietRecordResponse, error) {
	if recordID <= 0 {
		return nil, &ParameterError{Field: "recordId", Message: "must be positive"}
	}

	// 调用管理端饮食记录服务获取详情
	response, err := d.dietRecordAdminService.GetDietRecord(ctx, recordID)
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to get diet record detail: %w", err)
	}

	return response, nil
}

// GetPopularFoods 获取热门食物统计
func (d *dashboardLogic) GetPopularFoods(ctx context.Context, req *v1.PopularFoodsRequest) ([]map[string]interface{}, error) {
	// 参数验证
	if req.Limit <= 0 {
		return nil, &ParameterError{Field: "limit", Message: "must be positive"}
	}

	// 设置默认周期
	period := req.Period
	if period == "" {
		period = "month"
	}

	// 调用饮食记录服务获取热门食物
	popularFoods, err := d.dietRecordService.GetPopularFoodsByPeriod(ctx, period, req.Limit)
	if err != nil {
		return nil, fmt.Errorf("dashboard: failed to get popular foods: %w", err)
	}

	// 如果结果为空，返回空数组而不是nil
	if popularFoods == nil {
		return make([]map[string]interface{}, 0), nil
	}

	return popularFoods, nil
}
