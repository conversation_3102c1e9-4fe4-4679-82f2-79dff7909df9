package v1

import "time"

// ===== 营养统计相关结构体 =====

// NutritionStatReq 营养统计请求
type NutritionStatReq struct {
	Date string `form:"date" binding:"omitempty" validate:"omitempty,datetime=2006-01-02"` // 日期，格式：yyyy-MM-dd
}

// NutritionStatResponse 营养统计响应
type NutritionStatResponse struct {
	Date              string   `json:"date"`              // 日期，格式：yyyy-MM-dd
	Calorie           int      `json:"calorie"`           // 卡路里/热量（千卡）
	Protein           float64  `json:"protein"`           // 蛋白质（克）
	Carbs             float64  `json:"carbs"`             // 碳水化合物（克）
	Fat               float64  `json:"fat"`               // 脂肪（克）
	CaloriePercentage float64  `json:"caloriePercentage"` // 热量目标达成百分比
	ProteinPercentage float64  `json:"proteinPercentage"` // 蛋白质目标达成百分比
	CarbsPercentage   float64  `json:"carbsPercentage"`   // 碳水目标达成百分比
	FatPercentage     float64  `json:"fatPercentage"`     // 脂肪目标达成百分比
}

// ===== 营养趋势相关结构体 =====

// NutritionTrendReq 营养趋势请求
type NutritionTrendReq struct {
	StartDate string `form:"startDate" binding:"omitempty" validate:"omitempty,datetime=2006-01-02"` // 开始日期，格式：yyyy-MM-dd
	EndDate   string `form:"endDate" binding:"omitempty" validate:"omitempty,datetime=2006-01-02"`   // 结束日期，格式：yyyy-MM-dd
	Type      string `form:"type" binding:"omitempty,oneof=week month custom" validate:"omitempty,oneof=week month custom"` // 类型：week、month、custom
}



// ===== 营养详情相关结构体 =====

// NutritionDetailItemResponse 营养详情项响应
type NutritionDetailItemResponse struct {
	Name       string  `json:"name"`       // 营养素名称
	Value      float64 `json:"value"`      // 营养素值
	Unit       string  `json:"unit"`       // 营养素单位
	Percentage float64 `json:"percentage"` // 完成百分比
}

// ===== 营养建议相关结构体 =====

// NutritionAdviceReq 营养建议请求
type NutritionAdviceReq struct {
	Date string `form:"date" binding:"omitempty" validate:"omitempty,datetime=2006-01-02"` // 日期，格式：yyyy-MM-dd
}

// NutritionAdviceDisplayResponse 营养建议显示响应
type NutritionAdviceDisplayResponse struct {
	Type        string `json:"type"`        // 建议类型: warning, info, danger, success
	Title       string `json:"title"`       // 建议标题
	Description string `json:"description"` // 建议详情
}

// ===== 营养建议管理相关结构体 =====

// NutritionAdviceManageReq 营养建议管理请求
type NutritionAdviceManageReq struct {
	Type          string `json:"type" binding:"required,oneof=warning info danger success" validate:"required,oneof=warning info danger success"`                   // 建议类型: warning, info, danger, success
	Title         string `json:"title" binding:"required,min=1,max=100" validate:"required,min=1,max=100"`                                                        // 建议标题
	Description   string `json:"description" binding:"required,min=1,max=500" validate:"required,min=1,max=500"`                                                  // 建议详情
	ConditionType string `json:"conditionType" binding:"required,oneof=protein carbs fat calorie default" validate:"required,oneof=protein carbs fat calorie default"` // 条件类型: protein, carbs, fat, calorie, default
	MinPercentage *int   `json:"minPercentage" binding:"omitempty,min=0,max=1000" validate:"omitempty,min=0,max=1000"`                                            // 最小百分比阈值
	MaxPercentage *int   `json:"maxPercentage" binding:"omitempty,min=0,max=1000" validate:"omitempty,min=0,max=1000"`                                            // 最大百分比阈值
	IsDefault     bool   `json:"isDefault"`                                                                                                                        // 是否为默认建议
	Priority      int    `json:"priority" binding:"required,min=0,max=100" validate:"required,min=0,max=100"`                                                     // 优先级，数字越大优先级越高
	Status        int8   `json:"status" binding:"required,min=0,max=1" validate:"required,min=0,max=1"`                                                           // 状态：1-启用，0-禁用
}

// NutritionAdviceResponse 营养建议响应
type NutritionAdviceResponse struct {
	ID            int64      `json:"id"`            // 建议ID
	Type          string     `json:"type"`          // 建议类型: warning, info, danger, success
	Title         string     `json:"title"`         // 建议标题
	Description   string     `json:"description"`   // 建议详情
	ConditionType string     `json:"conditionType"` // 条件类型: protein, carbs, fat, calorie, default
	MinPercentage *int       `json:"minPercentage"` // 最小百分比阈值
	MaxPercentage *int       `json:"maxPercentage"` // 最大百分比阈值
	IsDefault     bool       `json:"isDefault"`     // 是否为默认建议
	Priority      int        `json:"priority"`      // 优先级
	Status        int8       `json:"status"`        // 状态
	CreatedAt     time.Time  `json:"createdAt"`     // 创建时间
	UpdatedAt     time.Time  `json:"updatedAt"`     // 更新时间
}

// NutritionAdviceQueryReq 营养建议查询请求
type NutritionAdviceQueryReq struct {
	Current       int     `form:"current" binding:"omitempty,min=1" validate:"omitempty,min=1"`                                                                    // 当前页，默认1
	Size          int     `form:"size" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"`                                                     // 每页大小，默认10
	ConditionType *string `form:"conditionType" binding:"omitempty,oneof=protein carbs fat calorie default" validate:"omitempty,oneof=protein carbs fat calorie default"` // 条件类型筛选
	Status        *int8   `form:"status" binding:"omitempty,min=0,max=1" validate:"omitempty,min=0,max=1"`                                                       // 状态筛选
	Keyword       string  `form:"keyword" binding:"omitempty,max=100" validate:"omitempty,max=100"`                                                              // 关键词搜索（标题、描述）
}

// NutritionAdviceListResponse 营养建议列表响应
type NutritionAdviceListResponse struct {
	Total   int64                       `json:"total"`   // 总记录数
	Records []*NutritionAdviceResponse  `json:"records"` // 营养建议列表
	Current int                         `json:"current"` // 当前页码
	Size    int                         `json:"size"`    // 每页大小
}

// ===== 健康报告相关结构体 =====

// HealthReportReq 健康报告请求
type HealthReportReq struct {
	Date string `form:"date" binding:"omitempty" validate:"omitempty,datetime=2006-01-02"` // 日期，格式：yyyy-MM-dd
}

// NutrientProgressResponse 营养素进度对比响应
type NutrientProgressResponse struct {
	LastWeek float64 `json:"lastWeek"` // 上周摄入量
	ThisWeek float64 `json:"thisWeek"` // 本周摄入量
}

// WeeklyProgressResponse 周进度响应
type WeeklyProgressResponse struct {
	Calorie *NutrientProgressResponse `json:"calorie"` // 热量对比
	Protein *NutrientProgressResponse `json:"protein"` // 蛋白质对比
	Carbs   *NutrientProgressResponse `json:"carbs"`   // 碳水化合物对比
	Fat     *NutrientProgressResponse `json:"fat"`     // 脂肪对比
}

// NutritionBalanceResponse 营养平衡响应
type NutritionBalanceResponse struct {
	Protein int `json:"protein"` // 蛋白质达成百分比
	Carbs   int `json:"carbs"`   // 碳水化合物达成百分比
	Fat     int `json:"fat"`     // 脂肪达成百分比
}

// HealthReportResponse 健康报告响应
type HealthReportResponse struct {
	Date             string                            `json:"date"`             // 日期
	NutritionStat    *NutritionStatResponse            `json:"nutritionStat"`    // 营养统计
	Advices          []*NutritionAdviceDisplayResponse `json:"advices"`          // 营养建议列表
	WeeklyProgress   *WeeklyProgressResponse           `json:"weeklyProgress"`   // 周进度
	Suggestion       string                            `json:"suggestion"`       // 建议
	HealthScore      int                               `json:"healthScore"`      // 健康分数
	ScoreChange      int                               `json:"scoreChange"`      // 分数变化
	NutritionBalance *NutritionBalanceResponse         `json:"nutritionBalance"` // 营养平衡
}

// ===== 通用响应结构体 =====

// NutritionAdviceUpdateReq 营养建议更新请求
type NutritionAdviceUpdateReq struct {
	ID            int64  `json:"-"`                                                                                                                               // 建议ID（从URL路径获取，不从JSON解析）
	Type          string `json:"type" binding:"omitempty,oneof=warning info danger success" validate:"omitempty,oneof=warning info danger success"`           // 建议类型: warning, info, danger, success
	Title         string `json:"title" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"`                                                  // 建议标题
	Description   string `json:"description" binding:"omitempty,min=1,max=500" validate:"omitempty,min=1,max=500"`                                            // 建议详情
	ConditionType string `json:"conditionType" binding:"omitempty,oneof=protein carbs fat calorie default" validate:"omitempty,oneof=protein carbs fat calorie default"` // 条件类型: protein, carbs, fat, calorie, default
	MinPercentage *int   `json:"minPercentage" binding:"omitempty,min=0,max=1000" validate:"omitempty,min=0,max=1000"`                                        // 最小百分比阈值
	MaxPercentage *int   `json:"maxPercentage" binding:"omitempty,min=0,max=1000" validate:"omitempty,min=0,max=1000"`                                        // 最大百分比阈值
	IsDefault     *bool  `json:"isDefault"`                                                                                                                    // 是否为默认建议
	Priority      *int   `json:"priority" binding:"omitempty,min=0,max=100" validate:"omitempty,min=0,max=100"`                                               // 优先级，数字越大优先级越高
	Status        *int8  `json:"status" binding:"omitempty,min=0,max=1" validate:"omitempty,min=0,max=1"`                                                     // 状态：1-启用，0-禁用
}

// NutritionAdviceStatusUpdateReq 营养建议状态更新请求
type NutritionAdviceStatusUpdateReq struct {
	Status int8 `json:"status" binding:"required,min=0,max=1" validate:"required,min=0,max=1"` // 状态（0-禁用，1-启用）
}
