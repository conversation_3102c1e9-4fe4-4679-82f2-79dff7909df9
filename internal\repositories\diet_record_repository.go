package repositories

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"strconv"
	"time"
)

// IDietRecordRepo 定义了饮食记录仓库需要实现的所有方法
type IDietRecordRepo interface {
	// 基础CRUD操作
	Create(dietRecord *entities.DietRecord) error
	GetByID(id int64) (*entities.DietRecord, error)
	GetByIDWithFoods(id int64) (*entities.DietRecord, error)
	Update(dietRecord *entities.DietRecord) error
	Delete(id int64) error
	BatchDelete(ids []int64) error

	// 查询方法
	GetByUserID(userID int64, offset, limit int) ([]*entities.DietRecord, int64, error)
	GetByUserIDWithFoods(userID int64, offset, limit int) ([]*entities.DietRecord, int64, error)
	GetByDateRange(userID int64, startDate, endDate time.Time, offset, limit int) ([]*entities.DietRecord, int64, error)
	GetByDateRangeWithFoods(userID int64, startDate, endDate time.Time, offset, limit int) ([]*entities.DietRecord, int64, error)
	GetByMealType(userID int64, mealType string, offset, limit int) ([]*entities.DietRecord, int64, error)
	GetByDate(userID int64, date time.Time) ([]*entities.DietRecord, error)
	GetByDateWithFoods(userID int64, date time.Time) ([]*entities.DietRecord, error)

	// 搜索和筛选
	Search(userID int64, startDate, endDate *time.Time, mealType *string, offset, limit int) ([]*entities.DietRecord, int64, error)
	SearchWithFoods(userID int64, startDate, endDate *time.Time, mealType *string, offset, limit int) ([]*entities.DietRecord, int64, error)

	// 营养统计
	GetNutritionStatsByDateRange(userID int64, startDate, endDate time.Time, mealType *string) (*NutritionStats, error)
	GetDailyNutritionStats(userID int64, startDate, endDate time.Time) ([]*DailyNutritionStats, error)

	// 权限检查
	CheckOwnership(id int64, userID int64) (bool, error)
	ExistsByUserAndDate(userID int64, date time.Time, mealType string) (bool, error)

	// 事务支持
	CreateWithTx(tx *gorm.DB, dietRecord *entities.DietRecord) error
	UpdateWithTx(tx *gorm.DB, dietRecord *entities.DietRecord) error
	DeleteWithTx(tx *gorm.DB, id int64) error

	// 批量查询方法（用于营养统计服务）
	// GetBatchDietRecordsForNutritionStat 批量获取多个用户在指定日期范围内的饮食记录
	GetBatchDietRecordsForNutritionStat(userIDs []int64, startDate, endDate time.Time) (map[int64]map[string][]*entities.DietRecord, error)

	// FindActiveUserIdsByDate 获取指定日期有饮食记录的活跃用户ID列表
	FindActiveUserIdsByDate(date time.Time) ([]int64, error)

	// FindActiveUserIdsByDateRange 获取指定日期范围内有饮食记录的活跃用户ID列表
	FindActiveUserIdsByDateRange(startDate, endDate time.Time) ([]int64, error)

	// CountByDate 统计指定日期的饮食记录数量
	CountByDate(date time.Time) (int, error)

	// FindPopularFoodsByPeriod 查询指定时间范围内的热门食物
	FindPopularFoodsByPeriod(startDate, endDate time.Time, limit int) ([]map[string]interface{}, error)
}

// NutritionStats 营养统计结构
type NutritionStats struct {
	TotalCalorie float64 `json:"totalCalorie"`
	TotalProtein float64 `json:"totalProtein"`
	TotalFat     float64 `json:"totalFat"`
	TotalCarbs   float64 `json:"totalCarbs"`
	RecordCount  int64   `json:"recordCount"`
}

// DailyNutritionStats 每日营养统计结构
type DailyNutritionStats struct {
	Date         time.Time `json:"date"`
	TotalCalorie float64   `json:"totalCalorie"`
	TotalProtein float64   `json:"totalProtein"`
	TotalFat     float64   `json:"totalFat"`
	TotalCarbs   float64   `json:"totalCarbs"`
	RecordCount  int64     `json:"recordCount"`
}

// dietRecordRepository 饮食记录仓储
type dietRecordRepository struct {
	db *gorm.DB
}

// NewDietRecordRepository 创建饮食记录仓储实例
func NewDietRecordRepository(db *gorm.DB) IDietRecordRepo {
	return &dietRecordRepository{
		db: db,
	}
}

// 确保 dietRecordRepository 实现了 IDietRecordRepo 接口
var _ IDietRecordRepo = &dietRecordRepository{}

// Create 创建新饮食记录
func (r *dietRecordRepository) Create(dietRecord *entities.DietRecord) error {
	if err := r.db.Create(dietRecord).Error; err != nil {
		return &DatabaseError{
			Operation: "create",
			Table:     "diet_records",
			Err:       err,
		}
	}
	return nil
}

// GetByID 根据ID获取饮食记录
func (r *dietRecordRepository) GetByID(id int64) (*entities.DietRecord, error) {
	var dietRecord entities.DietRecord
	if err := r.db.Where("id = ?", id).First(&dietRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &DietRecordNotFoundError{
				Field: "id",
				Value: strconv.FormatInt(id, 10),
			}
		}
		return nil, fmt.Errorf("dao: query diet record by id=%d: %w", id, err)
	}
	return &dietRecord, nil
}

// GetByIDWithFoods 根据ID获取饮食记录（包含食物明细）
func (r *dietRecordRepository) GetByIDWithFoods(id int64) (*entities.DietRecord, error) {
	var dietRecord entities.DietRecord
	if err := r.db.Preload("DietRecordFoods").Where("id = ?", id).First(&dietRecord).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &DietRecordNotFoundError{
				Field: "id",
				Value: strconv.FormatInt(id, 10),
			}
		}
		return nil, fmt.Errorf("dao: query diet record with foods by id=%d: %w", id, err)
	}
	return &dietRecord, nil
}

// Update 更新饮食记录信息
func (r *dietRecordRepository) Update(dietRecord *entities.DietRecord) error {
	if err := r.db.Save(dietRecord).Error; err != nil {
		return fmt.Errorf("dao: update diet record id=%d: %w", dietRecord.ID, err)
	}
	return nil
}

// Delete 删除饮食记录
func (r *dietRecordRepository) Delete(id int64) error {
	if err := r.db.Delete(&entities.DietRecord{}, id).Error; err != nil {
		return fmt.Errorf("dao: delete diet record id=%d: %w", id, err)
	}
	return nil
}

// BatchDelete 批量删除饮食记录
func (r *dietRecordRepository) BatchDelete(ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	if err := r.db.Where("id IN ?", ids).Delete(&entities.DietRecord{}).Error; err != nil {
		return fmt.Errorf("dao: batch delete diet records ids=%v: %w", ids, err)
	}
	return nil
}

// GetByUserID 根据用户ID获取饮食记录列表（分页）
func (r *dietRecordRepository) GetByUserID(userID int64, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count diet records by user userID=%d: %w", userID, err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list diet records by user userID=%d offset=%d limit=%d: %w", userID, offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetByUserIDWithFoods 根据用户ID获取饮食记录列表（分页，包含食物明细）
func (r *dietRecordRepository) GetByUserIDWithFoods(userID int64, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count diet records by user userID=%d: %w", userID, err)
	}

	// 获取分页数据（包含食物明细）
	if err := query.Preload("DietRecordFoods").Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list diet records with foods by user userID=%d offset=%d limit=%d: %w", userID, offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetByDateRange 根据日期范围获取饮食记录
func (r *dietRecordRepository) GetByDateRange(userID int64, startDate, endDate time.Time, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count diet records by date range userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list diet records by date range userID=%d startDate=%s endDate=%s offset=%d limit=%d: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetByDateRangeWithFoods 根据日期范围获取饮食记录（包含食物明细）
func (r *dietRecordRepository) GetByDateRangeWithFoods(userID int64, startDate, endDate time.Time, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count diet records by date range userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 获取分页数据（包含食物明细）
	if err := query.Preload("DietRecordFoods").Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list diet records with foods by date range userID=%d startDate=%s endDate=%s offset=%d limit=%d: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetByMealType 根据餐次类型获取饮食记录
func (r *dietRecordRepository) GetByMealType(userID int64, mealType string, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ? AND meal_type = ?", userID, mealType)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count diet records by meal type userID=%d mealType=%s: %w", userID, mealType, err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list diet records by meal type userID=%d mealType=%s offset=%d limit=%d: %w", userID, mealType, offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetByDate 根据日期获取饮食记录
func (r *dietRecordRepository) GetByDate(userID int64, date time.Time) ([]*entities.DietRecord, error) {
	var dietRecords []*entities.DietRecord

	if err := r.db.Where("user_id = ? AND date = ?", userID, date).Order("time ASC").Find(&dietRecords).Error; err != nil {
		return nil, fmt.Errorf("dao: get diet records by date userID=%d date=%s: %w", userID, date.Format("2006-01-02"), err)
	}

	return dietRecords, nil
}

// GetByDateWithFoods 根据日期获取饮食记录（包含食物明细）
func (r *dietRecordRepository) GetByDateWithFoods(userID int64, date time.Time) ([]*entities.DietRecord, error) {
	var dietRecords []*entities.DietRecord

	if err := r.db.Preload("DietRecordFoods").Where("user_id = ? AND date = ?", userID, date).Order("time ASC").Find(&dietRecords).Error; err != nil {
		return nil, fmt.Errorf("dao: get diet records with foods by date userID=%d date=%s: %w", userID, date.Format("2006-01-02"), err)
	}

	return dietRecords, nil
}

// Search 搜索饮食记录（支持日期范围和餐次类型筛选）
func (r *dietRecordRepository) Search(userID int64, startDate, endDate *time.Time, mealType *string, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ?", userID)

	// 添加日期范围筛选条件
	if startDate != nil {
		query = query.Where("date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("date <= ?", *endDate)
	}

	// 添加餐次类型筛选条件
	if mealType != nil {
		query = query.Where("meal_type = ?", *mealType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count search diet records userID=%d: %w", userID, err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: search diet records userID=%d offset=%d limit=%d: %w", userID, offset, limit, err)
	}

	return dietRecords, total, nil
}

// SearchWithFoods 搜索饮食记录（支持日期范围和餐次类型筛选，包含食物明细）
func (r *dietRecordRepository) SearchWithFoods(userID int64, startDate, endDate *time.Time, mealType *string, offset, limit int) ([]*entities.DietRecord, int64, error) {
	var dietRecords []*entities.DietRecord
	var total int64

	query := r.db.Model(&entities.DietRecord{}).Where("user_id = ?", userID)

	// 添加日期范围筛选条件
	if startDate != nil {
		query = query.Where("date >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("date <= ?", *endDate)
	}

	// 添加餐次类型筛选条件
	if mealType != nil {
		query = query.Where("meal_type = ?", *mealType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count search diet records with foods userID=%d: %w", userID, err)
	}

	// 获取分页数据（包含食物明细）
	if err := query.Preload("DietRecordFoods").Offset(offset).Limit(limit).Order("date DESC, time DESC").Find(&dietRecords).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: search diet records with foods userID=%d offset=%d limit=%d: %w", userID, offset, limit, err)
	}

	return dietRecords, total, nil
}

// GetNutritionStatsByDateRange 获取指定日期范围的营养统计
func (r *dietRecordRepository) GetNutritionStatsByDateRange(userID int64, startDate, endDate time.Time, mealType *string) (*NutritionStats, error) {
	query := r.db.Model(&entities.DietRecord{}).
		Select("SUM(total_calorie) as total_calorie, COUNT(*) as record_count").
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate)

	// 添加餐次类型筛选条件
	if mealType != nil {
		query = query.Where("meal_type = ?", *mealType)
	}

	var result struct {
		TotalCalorie float64 `json:"total_calorie"`
		RecordCount  int64   `json:"record_count"`
	}

	if err := query.Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("dao: get nutrition stats userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 获取详细营养信息（从食物明细表聚合）
	var nutritionResult struct {
		TotalProtein float64 `json:"total_protein"`
		TotalFat     float64 `json:"total_fat"`
		TotalCarbs   float64 `json:"total_carbs"`
	}

	nutritionQuery := r.db.Table("diet_record_foods drf").
		Select("SUM(drf.protein) as total_protein, SUM(drf.fat) as total_fat, SUM(drf.carbs) as total_carbs").
		Joins("JOIN diet_records dr ON drf.diet_record_id = dr.id").
		Where("dr.user_id = ? AND dr.date >= ? AND dr.date <= ?", userID, startDate, endDate)

	if mealType != nil {
		nutritionQuery = nutritionQuery.Where("dr.meal_type = ?", *mealType)
	}

	if err := nutritionQuery.Scan(&nutritionResult).Error; err != nil {
		return nil, fmt.Errorf("dao: get detailed nutrition stats userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	return &NutritionStats{
		TotalCalorie: result.TotalCalorie,
		TotalProtein: nutritionResult.TotalProtein,
		TotalFat:     nutritionResult.TotalFat,
		TotalCarbs:   nutritionResult.TotalCarbs,
		RecordCount:  result.RecordCount,
	}, nil
}

// GetDailyNutritionStats 获取每日营养统计
func (r *dietRecordRepository) GetDailyNutritionStats(userID int64, startDate, endDate time.Time) ([]*DailyNutritionStats, error) {
	var results []struct {
		Date         time.Time `json:"date"`
		TotalCalorie float64   `json:"total_calorie"`
		RecordCount  int64     `json:"record_count"`
	}

	// 获取每日基础统计
	if err := r.db.Model(&entities.DietRecord{}).
		Select("date, SUM(total_calorie) as total_calorie, COUNT(*) as record_count").
		Where("user_id = ? AND date >= ? AND date <= ?", userID, startDate, endDate).
		Group("date").
		Order("date ASC").
		Scan(&results).Error; err != nil {
		return nil, fmt.Errorf("dao: get daily nutrition stats userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 获取每日详细营养信息
	var nutritionResults []struct {
		Date         time.Time `json:"date"`
		TotalProtein float64   `json:"total_protein"`
		TotalFat     float64   `json:"total_fat"`
		TotalCarbs   float64   `json:"total_carbs"`
	}

	if err := r.db.Table("diet_record_foods drf").
		Select("dr.date, SUM(drf.protein) as total_protein, SUM(drf.fat) as total_fat, SUM(drf.carbs) as total_carbs").
		Joins("JOIN diet_records dr ON drf.diet_record_id = dr.id").
		Where("dr.user_id = ? AND dr.date >= ? AND dr.date <= ?", userID, startDate, endDate).
		Group("dr.date").
		Order("dr.date ASC").
		Scan(&nutritionResults).Error; err != nil {
		return nil, fmt.Errorf("dao: get daily detailed nutrition stats userID=%d startDate=%s endDate=%s: %w", userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 合并结果
	nutritionMap := make(map[string]*DailyNutritionStats)
	for _, result := range results {
		dateStr := result.Date.Format("2006-01-02")
		nutritionMap[dateStr] = &DailyNutritionStats{
			Date:         result.Date,
			TotalCalorie: result.TotalCalorie,
			RecordCount:  result.RecordCount,
		}
	}

	for _, nutrition := range nutritionResults {
		dateStr := nutrition.Date.Format("2006-01-02")
		if stats, exists := nutritionMap[dateStr]; exists {
			stats.TotalProtein = nutrition.TotalProtein
			stats.TotalFat = nutrition.TotalFat
			stats.TotalCarbs = nutrition.TotalCarbs
		}
	}

	// 转换为切片
	var dailyStats []*DailyNutritionStats
	for _, stats := range nutritionMap {
		dailyStats = append(dailyStats, stats)
	}

	return dailyStats, nil
}

// CheckOwnership 检查饮食记录是否属于指定用户
func (r *dietRecordRepository) CheckOwnership(id int64, userID int64) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecord{}).Where("id = ? AND user_id = ?", id, userID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check diet record ownership id=%d userID=%d: %w", id, userID, err)
	}
	return count > 0, nil
}

// ExistsByUserAndDate 检查用户在指定日期和餐次是否已有记录
func (r *dietRecordRepository) ExistsByUserAndDate(userID int64, date time.Time, mealType string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecord{}).Where("user_id = ? AND date = ? AND meal_type = ?", userID, date, mealType).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check diet record exists userID=%d date=%s mealType=%s: %w", userID, date.Format("2006-01-02"), mealType, err)
	}
	return count > 0, nil
}

// CreateWithTx 在事务中创建饮食记录
func (r *dietRecordRepository) CreateWithTx(tx *gorm.DB, dietRecord *entities.DietRecord) error {
	if err := tx.Create(dietRecord).Error; err != nil {
		return &DatabaseError{
			Operation: "create_with_tx",
			Table:     "diet_records",
			Err:       err,
		}
	}
	return nil
}

// UpdateWithTx 在事务中更新饮食记录
func (r *dietRecordRepository) UpdateWithTx(tx *gorm.DB, dietRecord *entities.DietRecord) error {
	if err := tx.Save(dietRecord).Error; err != nil {
		return fmt.Errorf("dao: update diet record with tx id=%d: %w", dietRecord.ID, err)
	}
	return nil
}

// DeleteWithTx 在事务中删除饮食记录
func (r *dietRecordRepository) DeleteWithTx(tx *gorm.DB, id int64) error {
	if err := tx.Delete(&entities.DietRecord{}, id).Error; err != nil {
		return fmt.Errorf("dao: delete diet record with tx id=%d: %w", id, err)
	}
	return nil
}

// GetBatchDietRecordsForNutritionStat 批量获取多个用户在指定日期范围内的饮食记录
// 专门用于营养统计的聚合查询，避免多次查询
func (r *dietRecordRepository) GetBatchDietRecordsForNutritionStat(userIDs []int64, startDate, endDate time.Time) (map[int64]map[string][]*entities.DietRecord, error) {
	if len(userIDs) == 0 {
		return make(map[int64]map[string][]*entities.DietRecord), nil
	}

	var dietRecords []*entities.DietRecord

	// 查询指定用户在指定日期范围内的所有饮食记录（包含食物明细）
	err := r.db.Preload("DietRecordFoods").
		Where("user_id IN ? AND date >= ? AND date <= ?", userIDs, startDate, endDate).
		Order("date DESC, time DESC").
		Find(&dietRecords).Error

	if err != nil {
		return nil, fmt.Errorf("dao: batch get diet records for nutrition stat userIDs=%v startDate=%s endDate=%s: %w",
			userIDs, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	// 按用户ID和日期分组
	result := make(map[int64]map[string][]*entities.DietRecord)

	for _, record := range dietRecords {
		userID := record.UserID
		dateStr := record.Date.Format("2006-01-02")

		// 按用户ID分组
		if result[userID] == nil {
			result[userID] = make(map[string][]*entities.DietRecord)
		}

		// 按日期分组
		result[userID][dateStr] = append(result[userID][dateStr], record)
	}

	return result, nil
}

// FindActiveUserIdsByDate 获取指定日期有饮食记录的活跃用户ID列表
func (r *dietRecordRepository) FindActiveUserIdsByDate(date time.Time) ([]int64, error) {
	var userIDs []int64

	err := r.db.Model(&entities.DietRecord{}).
		Select("DISTINCT user_id").
		Where("date = ?", date).
		Pluck("user_id", &userIDs).Error

	if err != nil {
		return nil, fmt.Errorf("dao: find active user ids by date=%s: %w", date.Format("2006-01-02"), err)
	}

	return userIDs, nil
}

// FindActiveUserIdsByDateRange 获取指定日期范围内有饮食记录的活跃用户ID列表
func (r *dietRecordRepository) FindActiveUserIdsByDateRange(startDate, endDate time.Time) ([]int64, error) {
	var userIDs []int64

	err := r.db.Model(&entities.DietRecord{}).
		Select("DISTINCT user_id").
		Where("date >= ? AND date <= ?", startDate, endDate).
		Pluck("user_id", &userIDs).Error

	if err != nil {
		return nil, fmt.Errorf("dao: find active user ids by date range startDate=%s endDate=%s: %w",
			startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), err)
	}

	return userIDs, nil
}

// CountByDate 统计指定日期的饮食记录数量
func (r *dietRecordRepository) CountByDate(date time.Time) (int, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecord{}).Where("date = ?", date).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("dao: count diet records by date=%s: %w", date.Format("2006-01-02"), err)
	}
	return int(count), nil
}

// FindPopularFoodsByPeriod 查询指定时间范围内的热门食物
func (r *dietRecordRepository) FindPopularFoodsByPeriod(startDate, endDate time.Time, limit int) ([]map[string]interface{}, error) {
	var results []map[string]interface{}

	// 执行SQL查询，与Java版本保持一致
	// SELECT drf.food_name, COUNT(*) as count
	// FROM diet_record_foods drf
	// JOIN diet_records dr ON drf.diet_record_id = dr.id
	// WHERE dr.date BETWEEN ? AND ?
	// GROUP BY drf.food_name
	// ORDER BY count DESC
	// LIMIT ?
	err := r.db.Table("diet_record_foods drf").
		Select("drf.food_name, COUNT(*) as count").
		Joins("JOIN diet_records dr ON drf.diet_record_id = dr.id").
		Where("dr.date BETWEEN ? AND ?", startDate, endDate).
		Group("drf.food_name").
		Order("count DESC").
		Limit(limit).
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("dao: find popular foods by period startDate=%s endDate=%s limit=%d: %w",
			startDate.Format("2006-01-02"), endDate.Format("2006-01-02"), limit, err)
	}

	// 转换结果格式，确保与Java版本一致
	var formattedResults []map[string]interface{}
	for _, result := range results {
		foodMap := make(map[string]interface{})
		foodMap["name"] = result["food_name"]
		foodMap["count"] = result["count"]
		formattedResults = append(formattedResults, foodMap)
	}

	return formattedResults, nil
}
