package logic

import (
	"context"
	"fmt"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/service"
)

// nutritionStatLogic 营养统计业务逻辑实现
type nutritionStatLogic struct {
	dietRecordService        service.IDietRecordService
	userService             service.IUserService
	userNutritionGoalService service.IUserNutritionGoalService
}

// NewNutritionStatLogic 创建营养统计业务逻辑实例
func NewNutritionStatLogic(
	dietRecordService service.IDietRecordService,
	userService service.IUserService,
	userNutritionGoalService service.IUserNutritionGoalService,
) service.INutritionStatService {
	return &nutritionStatLogic{
		dietRecordService:        dietRecordService,
		userService:             userService,
		userNutritionGoalService: userNutritionGoalService,
	}
}

// 确保 nutritionStatLogic 实现了 INutritionStatService 接口
var _ service.INutritionStatService = &nutritionStatLogic{}

// GetDailyNutritionStat 获取用户每日营养统计
func (n *nutritionStatLogic) GetDailyNutritionStat(ctx context.Context, userID int64, date string) (*v1.NutritionStatResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 解析日期
	_, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, &ParameterError{Field: "date", Message: "invalid date format, should be yyyy-MM-dd"}
	}

	// 获取用户营养目标
	nutritionGoal, err := n.userNutritionGoalService.GetNutritionGoal(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get nutrition goal: %w", err)
	}

	// 构建查询请求获取当日饮食记录
	queryReq := &v1.DietRecordQueryReq{
		Current:   1,
		Size:      1000, // 设置足够大的页面大小获取当日所有记录
		StartDate: &date,
		EndDate:   &date,
		UserID:    &userID,
	}

	dietRecordsResult, err := n.dietRecordService.ListDietRecords(ctx, userID, queryReq)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get diet records: %w", err)
	}

	// 初始化营养统计数据
	nutritionStat := &v1.NutritionStatResponse{
		Date:              date,
		Calorie:           0,
		Protein:           0.0,
		Carbs:             0.0,
		Fat:               0.0,
		CaloriePercentage: 0.0,
		ProteinPercentage: 0.0,
		CarbsPercentage:   0.0,
		FatPercentage:     0.0,
	}

	// 汇总当日营养数据
	for _, dietRecord := range dietRecordsResult.Records {
		if dietRecord.DietRecordFoods != nil {
			for _, food := range dietRecord.DietRecordFoods {
				nutritionStat.Calorie += int(food.Calories)
				nutritionStat.Protein += food.Protein
				nutritionStat.Carbs += food.Carbs
				nutritionStat.Fat += food.Fat
			}
		}
	}

	// 计算目标达成百分比
	if nutritionGoal != nil {
		if nutritionGoal.CalorieTarget != nil && *nutritionGoal.CalorieTarget > 0 {
			nutritionStat.CaloriePercentage = float64(nutritionStat.Calorie) * 100.0 / float64(*nutritionGoal.CalorieTarget)
		}
		if nutritionGoal.ProteinTarget != nil && *nutritionGoal.ProteinTarget > 0 {
			nutritionStat.ProteinPercentage = nutritionStat.Protein * 100.0 / float64(*nutritionGoal.ProteinTarget)
		}
		if nutritionGoal.CarbsTarget != nil && *nutritionGoal.CarbsTarget > 0 {
			nutritionStat.CarbsPercentage = nutritionStat.Carbs * 100.0 / float64(*nutritionGoal.CarbsTarget)
		}
		if nutritionGoal.FatTarget != nil && *nutritionGoal.FatTarget > 0 {
			nutritionStat.FatPercentage = nutritionStat.Fat * 100.0 / float64(*nutritionGoal.FatTarget)
		}
	}

	return nutritionStat, nil
}

// GetNutritionTrend 获取用户营养趋势
func (n *nutritionStatLogic) GetNutritionTrend(ctx context.Context, userID int64, req *v1.NutritionTrendReq) (*v1.NutritionTrendResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}
	if req == nil {
		return nil, &ParameterError{Field: "req", Message: "is required"}
	}

	// 解析日期范围
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, &ParameterError{Field: "startDate", Message: "invalid date format"}
	}
	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, &ParameterError{Field: "endDate", Message: "invalid date format"}
	}

	// 一次性批量获取整个日期范围的饮食记录
	batchRecords, err := n.dietRecordService.GetBatchDietRecordsForNutritionStat(ctx, []int64{userID}, req.StartDate, req.EndDate)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get batch diet records: %w", err)
	}

	// 获取用户的饮食记录
	userRecords := batchRecords[userID]
	if userRecords == nil {
		userRecords = make(map[string][]*v1.DietRecordResponse)
	}

	// 只查询一次用户营养目标
	nutritionGoal, err := n.userNutritionGoalService.GetNutritionGoal(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get nutrition goal: %w", err)
	}

	trendResponse := &v1.NutritionTrendResponse{
		DateList:    []string{},
		CalorieList: []int{},
		ProteinList: []float64{},
		CarbsList:   []float64{},
		FatList:     []float64{},
	}

	// 遍历日期范围，计算每日营养数据
	currentDate := startDate
	for !currentDate.After(endDate) {
		dateStr := currentDate.Format("2006-01-02")
		trendResponse.DateList = append(trendResponse.DateList, dateStr)

		// 获取当日饮食记录
		dayRecords := userRecords[dateStr]

		if dayRecords != nil && len(dayRecords) > 0 {
			// 计算当日营养摄入
			dailyStat := n.calculateNutritionFromRecordsWithGoal(dayRecords, nutritionGoal, currentDate)
			trendResponse.CalorieList = append(trendResponse.CalorieList, dailyStat.Calorie)
			trendResponse.ProteinList = append(trendResponse.ProteinList, dailyStat.Protein)
			trendResponse.CarbsList = append(trendResponse.CarbsList, dailyStat.Carbs)
			trendResponse.FatList = append(trendResponse.FatList, dailyStat.Fat)
		} else {
			// 如果当天没有饮食记录，添加0值
			trendResponse.CalorieList = append(trendResponse.CalorieList, 0)
			trendResponse.ProteinList = append(trendResponse.ProteinList, 0.0)
			trendResponse.CarbsList = append(trendResponse.CarbsList, 0.0)
			trendResponse.FatList = append(trendResponse.FatList, 0.0)
		}

		currentDate = currentDate.AddDate(0, 0, 1)
	}

	return trendResponse, nil
}

// GetNutritionDetails 获取用户营养摄入详情
func (n *nutritionStatLogic) GetNutritionDetails(ctx context.Context, userID int64, date string) ([]*v1.NutritionDetailItemResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 获取当日营养摄入统计（已包含百分比计算）
	nutritionStat, err := n.GetDailyNutritionStat(ctx, userID, date)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get daily nutrition stat: %w", err)
	}

	detailList := []*v1.NutritionDetailItemResponse{
		{
			Name:       "热量",
			Value:      float64(nutritionStat.Calorie),
			Unit:       "千卡",
			Percentage: nutritionStat.CaloriePercentage,
		},
		{
			Name:       "蛋白质",
			Value:      nutritionStat.Protein,
			Unit:       "克",
			Percentage: nutritionStat.ProteinPercentage,
		},
		{
			Name:       "碳水化合物",
			Value:      nutritionStat.Carbs,
			Unit:       "克",
			Percentage: nutritionStat.CarbsPercentage,
		},
		{
			Name:       "脂肪",
			Value:      nutritionStat.Fat,
			Unit:       "克",
			Percentage: nutritionStat.FatPercentage,
		},
	}

	return detailList, nil
}

// CalculateNutritionComplianceRate 计算营养达标率
func (n *nutritionStatLogic) CalculateNutritionComplianceRate(ctx context.Context, date string) (float64, error) {
	// 解析日期
	parsedDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return 0.0, &ParameterError{Field: "date", Message: "invalid date format"}
	}

	// 获取所有活跃用户ID列表
	activeUserIds, err := n.dietRecordService.FindActiveUserIdsByDate(ctx, date)
	if err != nil {
		return 0.0, fmt.Errorf("logic: failed to find active users: %w", err)
	}

	if len(activeUserIds) == 0 {
		return 0.0, nil // 如果没有活跃用户，返回0
	}

	// 批量获取所有用户当日的饮食记录
	batchDietRecords, err := n.dietRecordService.GetBatchDietRecordsForNutritionStat(ctx, activeUserIds, date, date)
	if err != nil {
		return 0.0, fmt.Errorf("logic: failed to get batch diet records: %w", err)
	}

	compliantUsers := 0

	// 遍历所有活跃用户，检查他们的营养达标情况
	for _, userID := range activeUserIds {
		// 从批量查询结果中获取用户当日的饮食记录
		userRecords := batchDietRecords[userID]
		if userRecords != nil {
			dayRecords := userRecords[date]
			if dayRecords != nil && len(dayRecords) > 0 {
				// 获取用户营养目标
				nutritionGoal, err := n.userNutritionGoalService.GetNutritionGoal(ctx, userID)
				if err == nil && nutritionGoal != nil {
					nutritionStat := n.calculateNutritionFromRecordsWithGoal(dayRecords, nutritionGoal, parsedDate)

					// 检查是否达标（热量、蛋白质、碳水和脂肪都达到目标的80%以上）
					isCompliant := nutritionStat.CaloriePercentage >= 80 &&
						nutritionStat.ProteinPercentage >= 80 &&
						nutritionStat.CarbsPercentage >= 80 &&
						nutritionStat.FatPercentage >= 80

					if isCompliant {
						compliantUsers++
					}
				}
			}
		}
	}

	// 计算达标率
	complianceRate := float64(compliantUsers) / float64(len(activeUserIds)) * 100

	return complianceRate, nil
}

// GetAllNutritionTrend 获取全体用户营养趋势
func (n *nutritionStatLogic) GetAllNutritionTrend(ctx context.Context, period string) (map[string]interface{}, error) {
	// 处理日期参数
	today := time.Now()
	var startDate time.Time
	endDate := today

	// 根据period设置日期范围
	switch period {
	case "week":
		startDate = today.AddDate(0, 0, -6) // 最近一周
	case "month":
		startDate = today.AddDate(0, 0, -29) // 最近一个月
	case "year":
		startDate = today.AddDate(0, 0, -364) // 最近一年
	default:
		startDate = today.AddDate(0, 0, -29) // 默认一个月
	}

	// 获取所有活跃用户ID列表
	activeUserIds, err := n.dietRecordService.FindActiveUserIdsByDateRange(ctx, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("logic: failed to find active users: %w", err)
	}

	if len(activeUserIds) == 0 {
		// 如果没有活跃用户，返回空数据
		return map[string]interface{}{
			"dateList":    []string{},
			"calorieList": []float64{},
			"proteinList": []float64{},
			"carbsList":   []float64{},
			"fatList":     []float64{},
		}, nil
	}

	// 批量获取所有用户在指定日期范围内的饮食记录
	batchDietRecords, err := n.dietRecordService.GetBatchDietRecordsForNutritionStat(ctx, activeUserIds, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get batch diet records: %w", err)
	}

	var dateList []string
	var calorieList []float64
	var proteinList []float64
	var carbsList []float64
	var fatList []float64

	// 遍历日期范围，计算每日平均营养数据
	currentDate := startDate
	for !currentDate.After(endDate) {
		dateStr := currentDate.Format("2006-01-02")
		dateList = append(dateList, dateStr)

		var totalCalorie float64
		var totalProtein float64
		var totalCarbs float64
		var totalFat float64
		userCount := 0

		for _, userID := range activeUserIds {
			// 从批量查询结果中获取用户当日的饮食记录
			userRecords := batchDietRecords[userID]
			if userRecords != nil {
				dayRecords := userRecords[dateStr]
				if dayRecords != nil && len(dayRecords) > 0 {
					// 获取用户营养目标
					nutritionGoal, err := n.userNutritionGoalService.GetNutritionGoal(ctx, userID)
					if err == nil && nutritionGoal != nil {
						nutritionStat := n.calculateNutritionFromRecordsWithGoal(dayRecords, nutritionGoal, currentDate)

						// 累加营养数据
						totalCalorie += float64(nutritionStat.Calorie)
						totalProtein += nutritionStat.Protein
						totalCarbs += nutritionStat.Carbs
						totalFat += nutritionStat.Fat
						userCount++
					}
				}
			}
		}

		// 计算平均值
		if userCount > 0 {
			calorieList = append(calorieList, totalCalorie/float64(userCount))
			proteinList = append(proteinList, totalProtein/float64(userCount))
			carbsList = append(carbsList, totalCarbs/float64(userCount))
			fatList = append(fatList, totalFat/float64(userCount))
		} else {
			// 如果当天没有数据，添加0
			calorieList = append(calorieList, 0.0)
			proteinList = append(proteinList, 0.0)
			carbsList = append(carbsList, 0.0)
			fatList = append(fatList, 0.0)
		}

		currentDate = currentDate.AddDate(0, 0, 1)
	}

	// 构建结果
	result := map[string]interface{}{
		"dateList":    dateList,
		"calorieList": calorieList,
		"proteinList": proteinList,
		"carbsList":   carbsList,
		"fatList":     fatList,
	}

	return result, nil
}

// calculateNutritionFromRecordsWithGoal 根据饮食记录列表和营养目标计算营养统计数据
func (n *nutritionStatLogic) calculateNutritionFromRecordsWithGoal(
	dayRecords []*v1.DietRecordResponse,
	nutritionGoal *v1.UserNutritionGoalResponse,
	date time.Time,
) *v1.NutritionStatResponse {
	// 初始化营养统计数据
	nutritionStat := &v1.NutritionStatResponse{
		Date:              date.Format("2006-01-02"),
		Calorie:           0,
		Protein:           0.0,
		Carbs:             0.0,
		Fat:               0.0,
		CaloriePercentage: 0.0,
		ProteinPercentage: 0.0,
		CarbsPercentage:   0.0,
		FatPercentage:     0.0,
	}

	// 汇总当日营养数据
	for _, dietRecord := range dayRecords {
		if dietRecord.DietRecordFoods != nil {
			for _, food := range dietRecord.DietRecordFoods {
				nutritionStat.Calorie += int(food.Calories)
				nutritionStat.Protein += food.Protein
				nutritionStat.Carbs += food.Carbs
				nutritionStat.Fat += food.Fat
			}
		}
	}

	// 计算目标达成百分比
	if nutritionGoal != nil {
		if nutritionGoal.CalorieTarget != nil && *nutritionGoal.CalorieTarget > 0 {
			nutritionStat.CaloriePercentage = float64(nutritionStat.Calorie) * 100.0 / float64(*nutritionGoal.CalorieTarget)
		}
		if nutritionGoal.ProteinTarget != nil && *nutritionGoal.ProteinTarget > 0 {
			nutritionStat.ProteinPercentage = nutritionStat.Protein * 100.0 / float64(*nutritionGoal.ProteinTarget)
		}
		if nutritionGoal.CarbsTarget != nil && *nutritionGoal.CarbsTarget > 0 {
			nutritionStat.CarbsPercentage = nutritionStat.Carbs * 100.0 / float64(*nutritionGoal.CarbsTarget)
		}
		if nutritionGoal.FatTarget != nil && *nutritionGoal.FatTarget > 0 {
			nutritionStat.FatPercentage = nutritionStat.Fat * 100.0 / float64(*nutritionGoal.FatTarget)
		}
	}

	return nutritionStat
}
